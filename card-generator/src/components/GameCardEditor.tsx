import { useState, useEffect } from "react";
import { useDeckStore } from "../hooks/useDeckStore";
import { GameCardRenderer } from "./GameCardRenderer";
import { CardBackRenderer } from "./CardBackRenderer";
import {
  CARD_DIMENSIONS,
  CARD_CATEGORIES,
  FACTION_COLORS,
} from "../types/card";
import { CARD_TEMPLATES } from "../utils/cardTemplates";
import type {
  Card,
  GodCard,
  FactionCard,
  EventCard,
  MiracleCard,
  CulturalDecreeCard,
  CardCategory,
  FactionColor,
  CardAge,
  CardCost,
  CardOrientation,
  CardAsset,
} from "../types/card";

export function GameCardEditor() {
  const {
    deck,
    selectedCardId,
    isLoading,
    createDeck,
    addCard,
    updateCard,
    selectCard,
    deleteCard,
    duplicateCard,
    updateBackImages,
    undo,
    redo,
    canUndo,
    canRedo,
  } = useDeckStore();

  const [showBleed, setShowBleed] = useState(false);
  const [cardTypeFilter, setCardTypeFilter] = useState<CardCategory | "all">(
    "all"
  );
  const [factionFilter, setFactionFilter] = useState<FactionColor | "all">(
    "all"
  );
  const [previewScale, setPreviewScale] = useState(2);

  const selectedCard = deck?.cards.find((c) => c.id === selectedCardId);

  useEffect(() => {
    // Only create a deck if loading is complete and there's no deck
    if (!isLoading && !deck) {
      createDeck("God is With Me Deck").catch(console.error);
    }
  }, [deck, createDeck, isLoading]);

  useEffect(() => {
    if (deck && deck.cards.length > 0 && !selectedCardId) {
      selectCard(deck.cards[0].id);
    }
  }, [deck, selectedCardId, selectCard]);

  const handleCardUpdate = (field: string, value: any) => {
    if (!selectedCardId) return;
    updateCard(selectedCardId, { [field]: value });
  };

  const handleImageUpload = async (
    file: File,
    field: "illustrationImage" | "backImage"
  ) => {
    if (!selectedCardId) return;

    try {
      // Create object URL for the uploaded file
      const url = URL.createObjectURL(file);
      updateCard(selectedCardId, { [field]: { url } });
    } catch (error) {
      console.error("Failed to upload image:", error);
      alert("Failed to upload image. Please try again.");
    }
  };

  const handleBackgroundImageUpload = async (
    file: File,
    category: CardCategory
  ) => {
    try {
      // Create object URL for the uploaded file
      const url = URL.createObjectURL(file);
      updateBackImages({ [category]: { url } });
    } catch (error) {
      console.error("Failed to upload background image:", error);
      alert("Failed to upload background image. Please try again.");
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading deck...</div>
      </div>
    );
  }

  if (!deck) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Creating deck...</div>
      </div>
    );
  }

  const dimensions = CARD_DIMENSIONS[deck.settings.cardSize];

  const getEffectiveDimensions = (card: Card) => {
    const cardOrientation = card.orientation || "portrait";
    return cardOrientation === "landscape"
      ? { ...dimensions, width: dimensions.height, height: dimensions.width }
      : dimensions;
  };

  const filteredCards = deck.cards.filter((card) => {
    if (cardTypeFilter !== "all" && card.category !== cardTypeFilter) {
      return false;
    }
    if (factionFilter !== "all" && card.category === "faction") {
      const factionCard = card as FactionCard;
      if (!factionCard.faction || factionCard.faction !== factionFilter) {
        return false;
      }
    }
    return true;
  });

  return (
    <div className="flex h-full bg-gray-100">
      {/* Sidebar - Card List */}
      <div className="w-80 bg-white border-r border-gray-300 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold mb-2">{deck.settings.name}</h2>

          {/* Add Card Templates */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">
              Add Card Type:
            </label>
            <div className="grid grid-cols-2 gap-1 text-xs">
              <button
                onClick={() => addCard("god")}
                className="bg-purple-500 text-white px-2 py-1 rounded hover:bg-purple-600"
              >
                神祇
              </button>
              <button
                onClick={() => addCard("event")}
                className="bg-orange-500 text-white px-2 py-1 rounded hover:bg-orange-600"
              >
                事件
              </button>
              <button
                onClick={() => addCard("faction_red")}
                className="bg-red-500 text-white px-2 py-1 rounded hover:bg-red-600"
              >
                红色
              </button>
              <button
                onClick={() => addCard("faction_yellow")}
                className="bg-yellow-500 text-black px-2 py-1 rounded hover:bg-yellow-600"
              >
                黄色
              </button>
              <button
                onClick={() => addCard("faction_green")}
                className="bg-green-500 text-white px-2 py-1 rounded hover:bg-green-600"
              >
                绿色
              </button>
              <button
                onClick={() => addCard("faction_purple")}
                className="bg-purple-500 text-white px-2 py-1 rounded hover:bg-purple-600"
              >
                紫色
              </button>
              <button
                onClick={() => addCard("faction_gray")}
                className="bg-gray-500 text-white px-2 py-1 rounded hover:bg-gray-600"
              >
                灰色
              </button>
              <button
                onClick={() => addCard("miracle")}
                className="bg-yellow-600 text-white px-2 py-1 rounded hover:bg-yellow-700"
              >
                奇迹
              </button>
            </div>
          </div>

          <div className="flex gap-2 mb-4">
            <button
              onClick={undo}
              disabled={!canUndo}
              className="px-3 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 disabled:opacity-50 text-sm"
            >
              Undo
            </button>
            <button
              onClick={redo}
              disabled={!canRedo}
              className="px-3 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 disabled:opacity-50 text-sm"
            >
              Redo
            </button>
          </div>

          {/* Filters */}
          <div className="space-y-2">
            <div>
              <label className="block text-xs font-medium mb-1">
                Card Type Filter
              </label>
              <select
                value={cardTypeFilter}
                onChange={(e) =>
                  setCardTypeFilter(e.target.value as CardCategory | "all")
                }
                className="w-full px-2 py-1 border border-gray-300 rounded text-xs"
              >
                <option value="all">All Types ({deck.cards.length})</option>
                <option value="god">
                  神祇卡 (
                  {deck.cards.filter((c) => c.category === "god").length})
                </option>
                <option value="faction">
                  派系卡 (
                  {deck.cards.filter((c) => c.category === "faction").length})
                </option>
                <option value="event">
                  事件卡 (
                  {deck.cards.filter((c) => c.category === "event").length})
                </option>
                <option value="miracle">
                  奇迹卡 (
                  {deck.cards.filter((c) => c.category === "miracle").length})
                </option>
                <option value="cultural_decree">
                  文化法令 (
                  {
                    deck.cards.filter((c) => c.category === "cultural_decree")
                      .length
                  }
                  )
                </option>
              </select>
            </div>

            {cardTypeFilter === "faction" && (
              <div>
                <label className="block text-xs font-medium mb-1">
                  Faction Color
                </label>
                <select
                  value={factionFilter}
                  onChange={(e) =>
                    setFactionFilter(e.target.value as FactionColor | "all")
                  }
                  className="w-full px-2 py-1 border border-gray-300 rounded text-xs"
                >
                  <option value="all">All Colors</option>
                  <option value="red">红色 (军事)</option>
                  <option value="yellow">黄色 (经济)</option>
                  <option value="green">绿色 (文化)</option>
                  <option value="purple">紫色 (信仰)</option>
                  <option value="gray">灰色 (建筑)</option>
                </select>
              </div>
            )}
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          {filteredCards.length === 0 ? (
            <div className="p-4 text-center text-gray-500 text-sm">
              No cards match the current filter
            </div>
          ) : (
            filteredCards.map((card) => {
              const cardIndex = deck.cards.findIndex((c) => c.id === card.id);
              const categoryInfo = CARD_CATEGORIES[card.category];
              let bgColor = "bg-white";

              if (card.category === "faction") {
                const factionCard = card as FactionCard;
                bgColor = factionCard.faction
                  ? FACTION_COLORS[factionCard.faction]?.bg || "bg-white"
                  : "bg-white";
              }

              return (
                <div
                  key={card.id}
                  className={`p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                    selectedCardId === card.id
                      ? "bg-blue-50 border-blue-200"
                      : ""
                  }`}
                  onClick={() => selectCard(card.id)}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">
                        {card.name || "Untitled Card"}
                      </h4>
                      <p className="text-xs text-gray-500">
                        {categoryInfo.name} - Card {cardIndex + 1}
                      </p>
                      {card.category === "faction" &&
                        (card as FactionCard).faction && (
                          <span
                            className={`inline-block text-xs px-2 py-1 rounded mt-1 ${bgColor} text-white`}
                          >
                            {
                              FACTION_COLORS[(card as FactionCard).faction]
                                ?.name
                            }
                          </span>
                        )}
                    </div>
                    <div className="flex gap-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          duplicateCard(card.id);
                        }}
                        className="text-xs px-2 py-1 bg-gray-200 rounded hover:bg-gray-300"
                      >
                        Copy
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (deck.cards.length > 1) {
                            deleteCard(card.id);
                          }
                        }}
                        disabled={deck.cards.length <= 1}
                        className="text-xs px-2 py-1 bg-red-200 text-red-700 rounded hover:bg-red-300 disabled:opacity-50"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Card Preview */}
        <div className="flex-1 p-8 flex flex-col relative">
          {selectedCard && (
            <>
              {/* Size Measurements - Top */}
              <div className="mb-6 flex justify-center">
                <div className="bg-white p-4 rounded shadow text-sm">
                  <h4 className="font-semibold mb-2">Card Dimensions</h4>
                  {(() => {
                    const dims = getEffectiveDimensions(selectedCard);
                    const bleed = dims.bleed;
                    return (
                      <div className="space-y-1 text-xs">
                        <div>
                          <strong>Card Size:</strong> {dims.width} ×{" "}
                          {dims.height} mm
                        </div>
                        <div>
                          <strong>With Bleed:</strong> {dims.width + bleed * 2}{" "}
                          × {dims.height + bleed * 2} mm
                        </div>
                        <div>
                          <strong>Bleed:</strong> {bleed} mm
                        </div>
                        <div>
                          <strong>DPI:</strong> {deck.settings.dpi}
                        </div>
                        <div className="pt-2 border-t">
                          <div>
                            <strong>Preview Scale:</strong>{" "}
                            {(previewScale * 100).toFixed(0)}%
                          </div>
                          <div>
                            <strong>Preview Size:</strong>{" "}
                            {Math.round(dims.width * previewScale)} ×{" "}
                            {Math.round(dims.height * previewScale)} px
                          </div>
                        </div>
                      </div>
                    );
                  })()}
                </div>
              </div>

              {/* Card Previews - Center */}
              <div className="flex-1 flex items-center justify-center">
                <div className="flex gap-4">
                  {/* Front Card */}
                  <div className="relative">
                    <div className="text-center mb-2">
                      <span className="text-sm font-medium text-gray-600">
                        Front
                      </span>
                    </div>
                    <GameCardRenderer
                      card={selectedCard}
                      dimensions={getEffectiveDimensions(selectedCard)}
                      scale={previewScale}
                      showBleed={showBleed}
                      className="shadow-lg"
                    />
                  </div>

                  {/* Back Card */}
                  <div className="relative">
                    <div className="text-center mb-2">
                      <span className="text-sm font-medium text-gray-600">
                        Back
                      </span>
                    </div>
                    <CardBackRenderer
                      card={selectedCard}
                      dimensions={getEffectiveDimensions(selectedCard)}
                      backImages={deck.backImages || {}}
                      scale={previewScale}
                      showBleed={showBleed}
                      className="shadow-lg"
                    />
                  </div>
                </div>
              </div>

              {/* Preview Controls - Bottom */}
              <div className="mt-6 flex justify-center">
                <div className="flex items-center gap-4 bg-white px-4 py-3 rounded shadow">
                  <label className="text-sm font-medium">Preview Scale:</label>
                  <select
                    value={previewScale}
                    onChange={(e) => setPreviewScale(Number(e.target.value))}
                    className="px-2 py-1 border border-gray-300 rounded text-sm"
                  >
                    <option value={1}>100%</option>
                    <option value={1.5}>150%</option>
                    <option value={2}>200%</option>
                    <option value={2.5}>250%</option>
                    <option value={3}>300%</option>
                  </select>
                  <div className="border-l border-gray-300 pl-4">
                    <label className="flex items-center gap-2 text-sm">
                      <input
                        type="checkbox"
                        checked={showBleed}
                        onChange={(e) => setShowBleed(e.target.checked)}
                      />
                      Show bleed
                    </label>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Properties Panel */}
        <div className="w-80 bg-white border-l border-gray-300 p-4 overflow-y-auto">
          {selectedCard && (
            <CardPropertiesEditor
              card={selectedCard}
              deck={deck}
              onUpdate={handleCardUpdate}
              onImageUpload={handleImageUpload}
              onBackgroundImageUpload={handleBackgroundImageUpload}
              onBackgroundImageUpdate={(
                category: CardCategory,
                asset?: CardAsset
              ) => {
                updateBackImages({ [category]: asset });
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
}

function CardPropertiesEditor({
  card,
  deck,
  onUpdate,
  onImageUpload,
  onBackgroundImageUpload,
  onBackgroundImageUpdate,
}: {
  card: Card;
  deck: any;
  onUpdate: (field: string, value: any) => void;
  onImageUpload: (file: File, field: "illustrationImage" | "backImage") => void;
  onBackgroundImageUpload: (file: File, category: CardCategory) => void;
  onBackgroundImageUpdate: (category: CardCategory, asset?: CardAsset) => void;
}) {
  const renderEditor = () => {
    switch (card.category) {
      case "god":
        return <GodCardEditor card={card as GodCard} onUpdate={onUpdate} />;
      case "faction":
        return (
          <FactionCardEditor card={card as FactionCard} onUpdate={onUpdate} />
        );
      case "event":
        return <EventCardEditor card={card as EventCard} onUpdate={onUpdate} />;
      case "miracle":
        return (
          <MiracleCardEditor card={card as MiracleCard} onUpdate={onUpdate} />
        );
      case "cultural_decree":
        return (
          <CulturalDecreeCardEditor
            card={card as CulturalDecreeCard}
            onUpdate={onUpdate}
          />
        );
      default:
        return <div>Unknown card type</div>;
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold mb-2">
          {CARD_CATEGORIES[card.category].name}
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          {CARD_CATEGORIES[card.category].description}
        </p>
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Card Name</label>
        <input
          type="text"
          value={card.name}
          onChange={(e) => onUpdate("name", e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">
          Illustration Image
        </label>
        <div className="space-y-2">
          <input
            type="url"
            placeholder="Enter image URL..."
            value={card.illustrationImage?.url || ""}
            onChange={(e) =>
              onUpdate(
                "illustrationImage",
                e.target.value ? { url: e.target.value } : undefined
              )
            }
            className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <div className="text-center text-xs text-gray-500">or</div>
          <label className="flex items-center justify-center w-full px-3 py-2 border-2 border-dashed border-gray-300 rounded cursor-pointer hover:border-gray-400 transition-colors">
            <div className="text-center">
              <svg
                className="mx-auto h-6 w-6 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                />
              </svg>
              <span className="text-xs text-gray-600">Upload image</span>
            </div>
            <input
              type="file"
              accept="image/*"
              onChange={(e) =>
                e.target.files?.[0] &&
                onImageUpload(e.target.files[0], "illustrationImage")
              }
              className="hidden"
            />
          </label>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">
          Background Image ({CARD_CATEGORIES[card.category].name})
        </label>
        <div className="space-y-2">
          <div className="text-xs text-gray-600 mb-2">
            This background image is shared across all{" "}
            {CARD_CATEGORIES[card.category].name} cards
          </div>
          <input
            type="url"
            placeholder="Enter image URL..."
            value={deck?.backImages?.[card.category]?.url || ""}
            onChange={(e) =>
              onBackgroundImageUpdate(
                card.category,
                e.target.value ? { url: e.target.value } : undefined
              )
            }
            className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <div className="text-center text-xs text-gray-500">or</div>
          <label className="flex items-center justify-center w-full px-3 py-2 border-2 border-dashed border-gray-300 rounded cursor-pointer hover:border-gray-400 transition-colors">
            <div className="text-center">
              <svg
                className="mx-auto h-6 w-6 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                />
              </svg>
              <span className="text-xs text-gray-600">Upload image</span>
            </div>
            <input
              type="file"
              accept="image/*"
              onChange={(e) =>
                e.target.files?.[0] &&
                onBackgroundImageUpload(e.target.files[0], card.category)
              }
              className="hidden"
            />
          </label>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">
          Card Back Image
        </label>
        <div className="space-y-2">
          <input
            type="url"
            placeholder="Enter image URL..."
            value={card.backImage?.url || ""}
            onChange={(e) =>
              onUpdate(
                "backImage",
                e.target.value ? { url: e.target.value } : undefined
              )
            }
            className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <div className="text-center text-xs text-gray-500">or</div>
          <label className="flex items-center justify-center w-full px-3 py-2 border-2 border-dashed border-gray-300 rounded cursor-pointer hover:border-gray-400 transition-colors">
            <div className="text-center">
              <svg
                className="mx-auto h-6 w-6 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                />
              </svg>
              <span className="text-xs text-gray-600">Upload image</span>
            </div>
            <input
              type="file"
              accept="image/*"
              onChange={(e) =>
                e.target.files?.[0] &&
                onImageUpload(e.target.files[0], "backImage")
              }
              className="hidden"
            />
          </label>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Orientation</label>
        <select
          value={card.orientation || "portrait"}
          onChange={(e) =>
            onUpdate("orientation", e.target.value as CardOrientation)
          }
          className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="portrait">Portrait</option>
          <option value="landscape">Landscape</option>
        </select>
      </div>

      {renderEditor()}
    </div>
  );
}

function GodCardEditor({
  card,
  onUpdate,
}: {
  card: GodCard;
  onUpdate: (field: string, value: any) => void;
}) {
  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-1">Faith Cost</label>
        <input
          type="text"
          value={card.faithCost}
          onChange={(e) =>
            onUpdate(
              "faithCost",
              isNaN(Number(e.target.value))
                ? e.target.value
                : Number(e.target.value)
            )
          }
          className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">
          Passive Ability
        </label>
        <textarea
          value={card.passive}
          onChange={(e) => onUpdate("passive", e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={3}
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Divine Power</label>
        <textarea
          value={card.divinePower}
          onChange={(e) => onUpdate("divinePower", e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={4}
        />
      </div>
    </div>
  );
}

function FactionCardEditor({
  card,
  onUpdate,
}: {
  card: FactionCard;
  onUpdate: (field: string, value: any) => void;
}) {
  const handleCostUpdate = (field: keyof CardCost, value: number) => {
    const currentCost = typeof card.cost === "string" ? {} : card.cost || {};
    const newCost = { ...currentCost, [field]: value || undefined };
    // Remove undefined values
    Object.keys(newCost).forEach((key) => {
      if (newCost[key as keyof CardCost] === undefined) {
        delete newCost[key as keyof CardCost];
      }
    });
    onUpdate("cost", newCost);
  };

  const getCostValue = (field: keyof CardCost): number => {
    if (typeof card.cost === "string") return 0;
    const value = card.cost?.[field];
    return typeof value === "number" ? value : 0;
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-1">Faction Color</label>
        <select
          value={card.faction}
          onChange={(e) => onUpdate("faction", e.target.value as FactionColor)}
          className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          {Object.entries(FACTION_COLORS).map(([key, value]) => (
            <option key={key} value={key}>
              {value.name}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Age</label>
        <select
          value={card.age}
          onChange={(e) => onUpdate("age", e.target.value as CardAge)}
          className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="extra">Extra</option>
          <option value="age1">Age 1</option>
          <option value="age2">Age 2</option>
          <option value="age3">Age 3</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Cost</label>
        <div className="grid grid-cols-3 gap-2">
          <div>
            <label className="text-xs text-gray-600">Gold</label>
            <input
              type="number"
              value={getCostValue("gold")}
              onChange={(e) => handleCostUpdate("gold", Number(e.target.value))}
              className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
            />
          </div>
          <div>
            <label className="text-xs text-gray-600">Culture</label>
            <input
              type="number"
              value={getCostValue("culture")}
              onChange={(e) =>
                handleCostUpdate("culture", Number(e.target.value))
              }
              className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
            />
          </div>
          <div>
            <label className="text-xs text-gray-600">Faith</label>
            <input
              type="number"
              value={getCostValue("faith")}
              onChange={(e) =>
                handleCostUpdate("faith", Number(e.target.value))
              }
              className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
            />
          </div>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Military Power</label>
        <input
          type="number"
          value={card.militaryPower || 0}
          onChange={(e) =>
            onUpdate("militaryPower", Number(e.target.value) || undefined)
          }
          className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Leader Effect</label>
        <textarea
          value={card.leaderEffect || ""}
          onChange={(e) =>
            onUpdate("leaderEffect", e.target.value || undefined)
          }
          className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={3}
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">
          Permanent Effect
        </label>
        <textarea
          value={card.permanentEffect || ""}
          onChange={(e) =>
            onUpdate("permanentEffect", e.target.value || undefined)
          }
          className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={3}
        />
      </div>
    </div>
  );
}

function EventCardEditor({
  card,
  onUpdate,
}: {
  card: EventCard;
  onUpdate: (field: string, value: any) => void;
}) {
  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-1">Effect</label>
        <textarea
          value={card.effect}
          onChange={(e) => onUpdate("effect", e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={4}
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Design Note</label>
        <textarea
          value={card.note || ""}
          onChange={(e) => onUpdate("note", e.target.value || undefined)}
          className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={2}
        />
      </div>
    </div>
  );
}

function MiracleCardEditor({
  card,
  onUpdate,
}: {
  card: MiracleCard;
  onUpdate: (field: string, value: any) => void;
}) {
  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-1">Victory Points</label>
        <input
          type="number"
          value={card.vp}
          onChange={(e) => onUpdate("vp", Number(e.target.value))}
          className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Condition</label>
        <textarea
          value={card.condition}
          onChange={(e) => onUpdate("condition", e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={3}
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">One-time Bonus</label>
        <textarea
          value={card.oneTimeBonus || ""}
          onChange={(e) =>
            onUpdate("oneTimeBonus", e.target.value || undefined)
          }
          className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={2}
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Suitable Route</label>
        <input
          type="text"
          value={card.suitableRoute || ""}
          onChange={(e) =>
            onUpdate("suitableRoute", e.target.value || undefined)
          }
          className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
    </div>
  );
}

function CulturalDecreeCardEditor({
  card,
  onUpdate,
}: {
  card: CulturalDecreeCard;
  onUpdate: (field: string, value: any) => void;
}) {
  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-1">Type</label>
        <select
          value={card.type || "basic"}
          onChange={(e) => onUpdate("type", e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="basic">Basic</option>
          <option value="advanced">Advanced</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Effect</label>
        <textarea
          value={card.effect}
          onChange={(e) => onUpdate("effect", e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={4}
        />
      </div>
    </div>
  );
}
